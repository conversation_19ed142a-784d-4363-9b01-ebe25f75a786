# Action Plan to Fix Polyline Area Selection Functionality

## Executive Summary

The polyline area selection feature in the WOSS Seismic Analysis Tool has several implementation gaps compared to the inline/crossline area selection features. This document provides a comprehensive analysis of the issues and a detailed action plan to bring polyline functionality to parity with other area selection methods.

**Revised Approach**: This plan has been reorganized to prioritize restoring the original working implementation first (Phase A), then adding enhancements (Phase B).

## Root Cause Analysis

### 1. **Inconsistent Data Structure Handling**

**Issue**: Polyline mode stores trace data differently than inline/crossline modes.

- **Inline/Crossline modes**: Store structured metadata including inline/crossline numbers, proper indexing
- **Polyline mode**: Only stores basic trace data without proper spatial context

**Impact**: 
- Export functionality cannot properly group traces
- View results cannot display proper axis labels
- Missing spatial reference information

**Note from Original Implementation**: The original app (in `initial_app/`) also had this limitation, storing only `trace_sample` and `trace_idx` without inline/crossline metadata.

### 2. **Missing Percentile Calculation for Polyline Mode**

**Issue**: Unlike well markers mode, polyline mode doesn't calculate HFC and Spectral Decrease percentile values during selection.

**Code Location**: `pages/select_area_page.py` lines 702-763

**Impact**:
- Normalization issues in view results
- Inconsistent descriptor calculations
- Export may produce incorrect normalized values

### 3. **Limited Export Grouping Options**

**Issue**: Polyline export doesn't properly handle grouping by inline/crossline.

**Code Location**: `pages/export_results_page.py`

**Current State**:
- Inline/crossline modes can group by their respective dimensions
- Polyline mode lacks proper grouping logic for spatial organization

**Impact**:
- Large, unorganized export files
- Difficult to process exported data
- No logical file organization

### 4. **Incomplete View Results Implementation**

**Issue**: Polyline mode uses generic trace indices for x-axis instead of meaningful spatial references.

**Code Location**: `pages/export_results_page.py` lines 1353-1356

**Current Implementation**:
```python
elif st.session_state.selection_mode == "By Polyline File Import":
    title_str += " - Polyline Section"
    x_axis_vals = np.arange(len(st.session_state.loaded_trace_data))
```

**Original Implementation Reference**: The original app used "Trace Position Along Polyline" as the x-axis label, which is more descriptive than just "Trace Index".

**Impact**:
- Poor visualization context
- Cannot correlate with spatial position
- Difficult to interpret results

### 5. **Missing Trace Metadata Storage**

**Issue**: Polyline selection doesn't store inline/crossline information for selected traces.

**Impact**:
- Cannot group by inline/crossline during export
- Missing spatial context in visualizations
- Incompatible with existing export infrastructure

## Implementation Plan - Two Phase Approach

## PHASE A: Restore Original Working Implementation (Priority: CRITICAL - Week 1)

### A.1: Fix View Results X-Axis Labels (Day 1)

**File**: `pages/export_results_page.py`

**Location**: Lines 1353-1356

**Simple Fix**:
```python
elif st.session_state.selection_mode == "By Polyline File Import":
    title_str += " - Polyline Section"
    x_axis_vals = np.arange(len(st.session_state.loaded_trace_data))
    x_axis_lbl = "Trace Position Along Polyline"  # Changed from "Trace Index"
```

### A.2: Add Percentile Calculation for Polyline Mode (Day 2-3)

**File**: `pages/select_area_page.py`

**Location**: After line 747, before storing loaded_trace_data

**Add** (copy the exact logic from well markers mode):
```python
# Calculate HFC and Spectral Decrease percentile values for polyline mode
if loaded_trace_data:
    try:
        from utils.processing import run_precomputation
        
        # Extract trace samples for processing
        original_traces = [item['trace_sample'] for item in loaded_trace_data]
        
        # Use default precomputation parameters
        default_precomputation_params = {
            "apply_smoothing": True,
            "smoothing_window": 5,
            "apply_normalization": True,
            "normalization_method": "Max Amplitude",
            "apply_filter": False,
            "filter_type": None,
            "filter_params": {}
        }
        
        # Run pre-computation to get descriptors
        processed_traces = run_precomputation(original_traces, default_precomputation_params)
        
        # Calculate HFC and Spectral Decrease percentile values
        hfc_values = []
        spec_decrease_values = []
        for desc in processed_traces:
            if hasattr(desc, 'get') and desc.get('hfc') is not None:
                hfc_values.extend(desc['hfc'])
            if hasattr(desc, 'get') and desc.get('spec_decrease') is not None:
                spec_decrease_values.extend(desc['spec_decrease'])
        
        if hfc_values:
            hfc_percentile = st.session_state.plot_settings.get('hfc_percentile', 95.0)
            hfc_pc = np.percentile(hfc_values, hfc_percentile)
            st.session_state.plot_settings['hfc_pc'] = float(hfc_pc)
            st.session_state.plot_settings['hfc_p95'] = float(hfc_pc)  # Backward compatibility
            logging.info(f"Polyline mode: Calculated HFC percentile cutoff (p{hfc_percentile}): {hfc_pc}")
        
        if spec_decrease_values:
            spec_decrease_percentile = st.session_state.plot_settings.get('spec_decrease_percentile', 95.0)
            spec_decrease_pc = np.percentile(spec_decrease_values, spec_decrease_percentile)
            st.session_state.plot_settings['spec_decrease_pc'] = float(spec_decrease_pc)
            st.session_state.plot_settings['spec_decrease_p95'] = float(spec_decrease_pc)  # Backward compatibility
            logging.info(f"Polyline mode: Calculated Spectral Decrease percentile cutoff (p{spec_decrease_percentile}): {spec_decrease_pc}")
            
    except Exception as e:
        logging.warning(f"Could not calculate percentile values for polyline mode: {e}")
        # Continue anyway - the robust helper functions will handle missing values
```

### A.3: Fix Basic Export Functionality (Day 4-5)

**File**: `pages/export_results_page.py`

**Ensure polyline mode can export with current grouping options**:

1. **Check grouping compatibility** (around line 250):
```python
# Add check for polyline mode
if st.session_state.selection_mode == "By Polyline File Import":
    # For polyline, we need to ensure traces have inline/crossline info
    # This might require loading header information for selected indices
    if grouping_type in ['inline', 'crossline']:
        st.warning(f"Note: Grouping by {grouping_type} for polyline selection may result in scattered groups.")
```

### A.4: Basic Testing (Day 5)

Create a simple test to verify:
1. Polyline file can be loaded
2. Traces are selected correctly
3. Percentile values are calculated
4. Export completes without errors
5. View results shows correct axis labels

**New File**: `tests/test_polyline_basic.py`

```python
#!/usr/bin/env python3
"""
Basic test to verify polyline functionality matches original implementation.
"""

import sys
sys.path.append('.')

def test_polyline_basic():
    """Test basic polyline functionality."""
    print("Testing polyline parsing...")
    # Test with test_polyline.txt
    
    print("Testing trace selection...")
    # Verify traces are selected
    
    print("Testing export...")
    # Verify export doesn't crash
    
    print("Basic polyline tests passed!")

if __name__ == "__main__":
    test_polyline_basic()
```

## PHASE B: Enhancements (Priority: MEDIUM - Week 2-4)

### B.1: Enhanced Data Structure (Week 2)

#### B.1.1 Store Inline/Crossline Metadata

**File**: `pages/select_area_page.py`

**Enhancement to trace loading**:
```python
# Line 736-747: Enhance trace data loading
loaded_trace_data = []
for idx in selected_indices:
    trace_sample = data_utils.load_trace_sample(st.session_state.header_loader.source_file_path, idx)
    if trace_sample is not None:
        # Get inline/crossline for this trace
        trace_mask = st.session_state.header_loader.unique_indices == idx
        inline = st.session_state.header_loader.inlines[trace_mask][0]
        crossline = st.session_state.header_loader.crosslines[trace_mask][0]
        
        loaded_trace_data.append({
            'trace_sample': trace_sample,
            'trace_idx': idx,
            'inline': inline,
            'crossline': crossline,
            'polyline_position': len(loaded_trace_data)  # Sequential position along polyline
        })
```

### B.2: Advanced Export Options (Week 3)

#### B.2.1 Sequential Grouping Option

**File**: `pages/export_results_page.py`

Add sequential grouping option for polyline mode:
```python
if st.session_state.selection_mode == "By Polyline File Import":
    grouping_options = ["sequential", "inline", "crossline"]
    default_grouping = "sequential"
    grouping_type = st.selectbox(
        "Group Export Files By:",
        options=grouping_options,
        index=grouping_options.index(default_grouping),
        key="export_grouping_select",
        help="Sequential: Order along polyline, Inline/Crossline: Group by seismic coordinates"
    )
```

### B.3: Distance Along Polyline (Week 3)

Add distance calculation for better spatial reference (see original Phase 1.2).

### B.4: Comprehensive Testing (Week 4)

Expand testing to cover all enhancements.

### B.5: Documentation (Week 4)

Create user guide for polyline functionality.

## Success Criteria

### Phase A (Must Have):
1. ✓ Polyline export completes without errors
2. ✓ View results shows "Trace Position Along Polyline" as x-axis label
3. ✓ Percentile values are calculated correctly
4. ✓ Basic functionality matches original implementation

### Phase B (Nice to Have):
1. ✓ Inline/crossline metadata stored for all traces
2. ✓ Sequential grouping option available
3. ✓ Distance along polyline calculated
4. ✓ Comprehensive documentation

## Risk Mitigation

1. **Phase A Focus**: By focusing on restoring original functionality first, we minimize risk of breaking changes
2. **Incremental Testing**: Test each change in Phase A before moving to next
3. **Backward Compatibility**: Ensure existing polyline files continue to work throughout both phases
4. **Performance Monitoring**: Check that percentile calculation doesn't significantly slow down polyline selection

## Revised Timeline

### Week 1 (Phase A - Critical):
- Day 1: Fix view results labels (A.1)
- Day 2-3: Add percentile calculation (A.2)
- Day 4-5: Fix export and test (A.3, A.4)

### Week 2-4 (Phase B - Enhancements):
- Week 2: Enhanced data structures (B.1)
- Week 3: Export options and distance calculation (B.2, B.3)
- Week 4: Testing and documentation (B.4, B.5)

## Conclusion

This revised action plan prioritizes restoring the original working implementation in Phase A before adding enhancements in Phase B. This approach ensures that polyline functionality works correctly as soon as possible, with improvements added incrementally afterward.