"""
Session state management for the WOSS Seismic Analysis Tool.
This module handles initialization and reset of session state variables.
"""

import streamlit as st
import os
import shutil
import tempfile
import logging
# Import numpy if needed for future use
# import numpy as np

def initialize_session_state():
    """Initialize all session state variables with default values if they don't exist."""
    # File information
    if 'segy_file_info' not in st.session_state:
        st.session_state.segy_file_info = None
    if 'well_file_info' not in st.session_state:
        st.session_state.well_file_info = None
    if 'polyline_file_info' not in st.session_state:
        st.session_state.polyline_file_info = None

    # Data loaders and basic properties
    if 'header_loader' not in st.session_state:
        st.session_state.header_loader = None
    if 'well_df' not in st.session_state:
        st.session_state.well_df = None
    if 'dt' not in st.session_state:
        st.session_state.dt = None
    if 'trace_count' not in st.session_state:
        st.session_state.trace_count = None

    # Analysis settings and state
    if 'stats_defaults' not in st.session_state:
        st.session_state.stats_defaults = None
    if 'current_step' not in st.session_state:
        st.session_state.current_step = "load_data"
    if 'plot_settings' not in st.session_state:
        st.session_state.plot_settings = {'hfc_percentile': 95.0}
    if 'display_params_configured' not in st.session_state:
        st.session_state.display_params_configured = False

    # Selection mode and parameters
    if 'mode_selector' not in st.session_state:
        st.session_state.mode_selector = None
    if 'selection_mode' not in st.session_state:
        st.session_state.selection_mode = None
    if 'force_mode_rerun' not in st.session_state:
        st.session_state.force_mode_rerun = False
    if 'selected_well_markers' not in st.session_state:
        st.session_state.selected_well_markers = []
    if 'plot_twt' not in st.session_state:
        st.session_state.plot_twt = False
    if 'plot_mode_wells' not in st.session_state:
        st.session_state.plot_mode_wells = 1
    if 'well_analysis_sub_option' not in st.session_state:
        st.session_state.well_analysis_sub_option = None
    if 'selected_well_marker_pairs' not in st.session_state:
        st.session_state.selected_well_marker_pairs = []

    # Inline/Crossline selection
    if 'selected_inline' not in st.session_state:
        st.session_state.selected_inline = None
    if 'selected_crossline' not in st.session_state:
        st.session_state.selected_crossline = None

    # AOI parameters
    if 'aoi_inline_min' not in st.session_state:
        st.session_state.aoi_inline_min = None
    if 'aoi_inline_max' not in st.session_state:
        st.session_state.aoi_inline_max = None
    if 'aoi_xline_min' not in st.session_state:
        st.session_state.aoi_xline_min = None
    if 'aoi_xline_max' not in st.session_state:
        st.session_state.aoi_xline_max = None
    if 'aoi_processing_option' not in st.session_state:
        st.session_state.aoi_processing_option = "Full AOI"  # Default to Full AOI
    if 'aoi_plot_section_type' not in st.session_state:
        st.session_state.aoi_plot_section_type = None
    if 'aoi_plot_fixed_value' not in st.session_state:
        st.session_state.aoi_plot_fixed_value = None

    # Polyline parameters
    if 'polyline_vertices' not in st.session_state:
        st.session_state.polyline_vertices = None
    if 'polyline_tolerance' not in st.session_state:
        st.session_state.polyline_tolerance = 0.0

    # Area selection and pre-computation
    if 'area_selected' not in st.session_state:
        st.session_state.area_selected = False
    if 'area_selected_details' not in st.session_state:
        st.session_state.area_selected_details = None
    if 'selected_data_for_precompute' not in st.session_state:
        st.session_state.selected_data_for_precompute = None
    if 'precomputation_params' not in st.session_state:
        st.session_state.precomputation_params = {}
    if 'precomputation_complete' not in st.session_state:
        st.session_state.precomputation_complete = False
    if 'precomputed_data_output' not in st.session_state:
        st.session_state.precomputed_data_output = None
    if 'qc_results_info' not in st.session_state:
        st.session_state.qc_results_info = None
    if 'auto_run_precomputation' not in st.session_state:
        st.session_state.auto_run_precomputation = False

    # Analysis data
    if 'selected_indices' not in st.session_state:
        st.session_state.selected_indices = []
    if 'loaded_trace_data' not in st.session_state:
        st.session_state.loaded_trace_data = []
    if 'calculated_descriptors' not in st.session_state:
        st.session_state.calculated_descriptors = []
    if 'analysis_complete' not in st.session_state:
        st.session_state.analysis_complete = False
    if 'descriptor_statistics' not in st.session_state:
        st.session_state.descriptor_statistics = {}
    if 'selected_outputs' not in st.session_state:
        st.session_state.selected_outputs = []
    if 'batch_size' not in st.session_state:
        st.session_state.batch_size = None

    # Trace loading flags for different modes
    if 'traces_loaded_inline' not in st.session_state:
        st.session_state.traces_loaded_inline = False
    if 'traces_loaded_crossline' not in st.session_state:
        st.session_state.traces_loaded_crossline = False
    if 'traces_loaded_polyline' not in st.session_state:
        st.session_state.traces_loaded_polyline = False
    if 'traces_loaded_aoi' not in st.session_state:
        st.session_state.traces_loaded_aoi = False
    if 'traces_loaded_wells' not in st.session_state:
        st.session_state.traces_loaded_wells = False
    if 'inline_descriptors_calculated' not in st.session_state:
        st.session_state.inline_descriptors_calculated = False
    if 'crossline_descriptors_calculated' not in st.session_state:
        st.session_state.crossline_descriptors_calculated = False
    if 'area_selected_mode' not in st.session_state:
        st.session_state.area_selected_mode = None

    # Data validation and loading state management
    if 'data_validation_status' not in st.session_state:
        st.session_state.data_validation_status = None
    if 'data_loading_in_progress' not in st.session_state:
        st.session_state.data_loading_in_progress = False
    if 'data_ready_for_analysis' not in st.session_state:
        st.session_state.data_ready_for_analysis = False
    if 'last_validation_timestamp' not in st.session_state:
        st.session_state.last_validation_timestamp = None
    if 'validation_required' not in st.session_state:
        st.session_state.validation_required = True

    # Export parameters
    if 'export_attributes' not in st.session_state:
        st.session_state.export_attributes = []
    if 'export_grouping' not in st.session_state:
        st.session_state.export_grouping = None
    if 'export_batch_step' not in st.session_state:
        st.session_state.export_batch_step = 1
    if 'export_in_progress' not in st.session_state:
        st.session_state.export_in_progress = False
    if 'exported_files_info' not in st.session_state:
        st.session_state.exported_files_info = None
    if 'export_output_dir' not in st.session_state:
        st.session_state.export_output_dir = None

    # Temporary file paths
    if 'segy_temp_file_path' not in st.session_state:
        st.session_state.segy_temp_file_path = None

    # UI control flags
    if 'use_wells' not in st.session_state:
        st.session_state.use_wells = True
    if 'scaler_mode' not in st.session_state:
        st.session_state.scaler_mode = "Use Scaler Byte"
    if 'custom_scaler' not in st.session_state:
        st.session_state.custom_scaler = 1.0

    # Basemap settings
    if 'show_basemap' not in st.session_state:
        st.session_state.show_basemap = False
    if 'selected_surfaces_for_basemap' not in st.session_state:
        st.session_state.selected_surfaces_for_basemap = []
    if 'basemap_figure' not in st.session_state:
        st.session_state.basemap_figure = None
    if 'basemap_needs_update' not in st.session_state:
        st.session_state.basemap_needs_update = False
    if 'basemap_detail_level' not in st.session_state:
        st.session_state.basemap_detail_level = 'low'
    if 'hfc_p95' not in st.session_state:
        st.session_state.hfc_p95 = None

def reset_state():
    # Reset HFC percentile session value
    st.session_state.hfc_percentile_value = None
    """Resets the session state for a new analysis."""
    # 1. Clean up temporary SEG-Y file if it exists
    if 'segy_temp_file_path' in st.session_state and st.session_state.segy_temp_file_path:
        try:
            if os.path.exists(st.session_state.segy_temp_file_path):
                os.remove(st.session_state.segy_temp_file_path)
                logging.info(f"Removed temporary SEG-Y file: {st.session_state.segy_temp_file_path}")
        except OSError as e:
            logging.warning(f"Could not remove temporary SEG-Y file {st.session_state.segy_temp_file_path}: {e}")
        except Exception as e:
             logging.error(f"Error during temporary SEG-Y file cleanup: {e}", exc_info=True)

    # 2. Clean up export directory if it exists
    if 'export_output_dir' in st.session_state and st.session_state.export_output_dir:
        try:
            if os.path.exists(st.session_state.export_output_dir):
                shutil.rmtree(st.session_state.export_output_dir)
                logging.info(f"Removed export directory: {st.session_state.export_output_dir}")
        except Exception as e:
            logging.warning(f"Could not remove export directory {st.session_state.export_output_dir}: {e}")

    # 3. Clean up any temporary files in the system temp directory with our prefix
    try:
        temp_dir = tempfile.gettempdir()
        for item in os.listdir(temp_dir):
            if item.startswith("woss_") and os.path.isdir(os.path.join(temp_dir, item)):
                try:
                    shutil.rmtree(os.path.join(temp_dir, item))
                    logging.info(f"Removed temporary directory: {os.path.join(temp_dir, item)}")
                except Exception as e:
                    logging.warning(f"Could not remove temporary directory {item}: {e}")
    except Exception as e:
        logging.warning(f"Error while cleaning up temporary directories: {e}")

    # 4. Clean up well marker data and processing outputs
    if 'selected_well_markers' in st.session_state and st.session_state.selected_well_markers:
        st.session_state.selected_well_markers = []
        logging.info("Reset selected well markers")

    if 'loaded_trace_data' in st.session_state and st.session_state.loaded_trace_data:
        st.session_state.loaded_trace_data = []
        logging.info("Cleared loaded trace data")

    if 'calculated_descriptors' in st.session_state and st.session_state.calculated_descriptors:
        st.session_state.calculated_descriptors = []
        logging.info("Cleared calculated descriptors")

    # 5. Reset mode selection flags and validation states
    for flag in ['traces_loaded_inline', 'traces_loaded_crossline', 'traces_loaded_polyline',
                 'traces_loaded_aoi', 'traces_loaded_wells']:
        if flag in st.session_state:
            st.session_state[flag] = False
            logging.info(f"Reset {flag} flag")

    # Reset validation states
    validation_keys = ['data_validation_status', 'data_loading_in_progress',
                      'data_ready_for_analysis', 'last_validation_timestamp']
    for key in validation_keys:
        if key in st.session_state:
            st.session_state[key] = None if key != 'data_loading_in_progress' else False
            logging.info(f"Reset {key}")

    # Mark validation as required for new analysis
    st.session_state.validation_required = True

    # 6. Keep track of keys to reset
    keys_to_reset = [
        'segy_file_info', 'well_file_info', 'polyline_file_info',
        'header_loader', 'well_df', 'dt', 'trace_count', 'stats_defaults',
        'current_step', 'plot_settings', 'mode_selector', 'selection_mode', 'force_mode_rerun', 'display_params_configured',
        'selected_well_markers', 'plot_twt', 'plot_mode_wells', 'well_analysis_sub_option', 'selected_well_marker_pairs',
        'selected_inline', 'selected_crossline',
        'aoi_inline_min', 'aoi_inline_max', 'aoi_xline_min', 'aoi_xline_max',
        'aoi_processing_option', 'aoi_plot_section_type', 'aoi_plot_fixed_value',
        'polyline_vertices', 'polyline_tolerance', 'selected_indices',
        # Pre-computation related keys
        'area_selected', 'area_selected_details', 'selected_data_for_precompute', 'precomputation_params',
        'precomputation_complete', 'precomputed_data_output', 'qc_results_info', 'auto_run_precomputation',
        # Analysis related keys
        'loaded_trace_data', 'calculated_descriptors', 'analysis_complete', 'descriptor_statistics',
        'selected_outputs', 'batch_size', 'export_attributes', 'export_grouping',
        'export_batch_step', 'export_in_progress', 'exported_files_info',
        'export_output_dir', 'segy_temp_file_path', 'use_wells',
        'scaler_mode', 'custom_scaler', 'show_basemap', 'selected_surfaces_for_basemap',
        'basemap_figure', 'basemap_needs_update', 'basemap_detail_level',
        'traces_loaded_inline', 'traces_loaded_crossline', 'traces_loaded_polyline',
        'traces_loaded_aoi', 'traces_loaded_wells', 'data_validation_status',
        'data_loading_in_progress', 'data_ready_for_analysis', 'last_validation_timestamp',
        'validation_required'
    ]
    for key in keys_to_reset:
        if key in st.session_state:
            del st.session_state[key]

    # 7. Clear relevant cache too (Removed imports and calls to non-existent cached functions)
    # The caching mechanism needs to be implemented differently if desired.

    # 8. Set the current step back to load_data
    st.session_state.current_step = "load_data"
    logging.info("Session state reset for new analysis.")
